
import { ClassFactory } from "./Game/GameInit";
import { MyAssembler } from "./MyAssembler";

const {ccclass, property} = cc._decorator;


@ccclass
export default class MySprite extends cc.Sprite {
    // @property(cc.Vec2)
    // moveSpeed: cc.Vec2 = cc.Vec2.ZERO;

    // 将自定义数据传递给assembler，在设置完所有参数后调用
    // 也可以在moveSpeed setter方法内主动传值，需要调用setVertsDirty()使顶点数据重算
    public FlushProperties() {
        const myAssembler = ClassFactory.Instance.MyAssembler();
        let assembler: myAssembler = this._assembler;
        if (!assembler)
            return;

        // assembler.moveSpeed = this.moveSpeed;
        this.setVertsDirty();
    }

    _resetAssembler () {
        const myAssembler = ClassFactory.Instance.MyAssembler();
        this.setVertsDirty();
        let assembler = this._assembler = new myAssembler();
        this.FlushProperties();

        assembler.init(this);
    }
}