import { MyAssembler } from "../MyAssembler";


let gameInit = {
    x: ()=> {
        // console.log('init!');
    },
};

cc.game.once(cc.game.EVENT_GAME_INITED, (...args)=>{
    console.log('-------EVENT_GAME_INITED-------');
    // console.log(args);
    if(CC_DEBUG) {
        console.log(cc);
        gameInit.x();
    }
});

export class ClassFactory {
    public static get Instance(): ClassFactory {
        if(this._instance) {
            return this._instance;
        } else {
            this._instance = new ClassFactory();
            this._instance._Init();
            return this._instance;
        }
    }

    private static _instance: ClassFactory = null;

    _inited = false;

    _Init() {
        console.log('ClassFactory Init!');

    }

    get MyAssembler(): typeof MyAssembler {
        if(this._inited) {
            return MyAssembler;
        } else {
            return null;
        }
    };

    public static CreateInstance<T>(type: {new(): T}): T {
        return new type();
    }
}