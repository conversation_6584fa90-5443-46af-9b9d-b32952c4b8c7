
declare namespace cc {
    export class Assembler {
        getVfmt(): any;
    }

    export class Assembler2D extends Assembler {
        floatsPerVert: number;
        verticesCount: number;
        indicesCount: number;
        uvOffset: number;
        colorOffset: number;
        
        _renderData: cc.RenderData;
        _local: number[];

        constructor();
        updateRenderData(comp: cc.RenderComponent): void;
        fillBuffers(comp: cc.RenderComponent, renderer: any): void;
        init(comp: cc.RenderComponent): void;
        updateWorldVerts(comp: cc.RenderComponent): void;
        packToDynamicAtlas(comp: cc.RenderComponent, frame: cc.SpriteFrame): void;
        // 这里你可以加上你需要的其它方法签名
    }

}

declare namespace cc {
    export interface RenderComponent extends cc.Component {
        setVertsDirty(): void;
    }

    export interface Sprite extends cc.RenderComponent {
        _assembler: cc.Assembler;
    }

    export namespace renderer {
        export const _handle: {
            getBuffer(type: string, format: any): any;
            // _batchedModels?: any[];  // 用于 GameUtils.ts 中的使用
        };
    }
}